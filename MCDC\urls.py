"""
URL configuration for MCDC project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularRedocView, SpectacularSwaggerView
from rest_framework.routers import DefaultRouter
from authentication.views import MemberView, callback_thaid
from utils.well_known_views import apple_app_site_association, android_app_site_association

router = DefaultRouter()
router.register(r'users', MemberView)

urlpatterns = [
    path("admin/", admin.site.urls),

    # Well-known static resources for iOS (Apple)
    path(".well-known/apple-app-site-association", apple_app_site_association, name="apple_app_site_association"),
    # Well-known static resources for Android (Google)
    path(".well-known/assetlinks.json", android_app_site_association, name="assetlinks"),


    # ThaID Callback URL (must be before authentication URLs to match specific path)
    path("api/callback/thaid/", callback_thaid, name="callback_thaid"),

    # Authentication URLs
    path("api/", include('authentication.urls')),

    # Chat URLs
    path("api/chat/", include('chat.urls')),
    
    # Search URLs
    path("api/search/", include('search.urls')),

    # Project URLs
    path("api/project/", include('project.urls')),

    # FAQ URLs
    path("api/faq/", include('faq.urls')),

    # Payment URLs
    path("api/payment/", include('payment.urls')),

    # Tracking URLs
    path("api/tracking/", include('tracking.urls')),

    # Dashboard URLs
    path("api/", include('dashboard.urls')),

    # Notification URLs
    path("api/notification/", include('notification.urls')),

    # Surveys URLs
    path("api/surveys/", include('surveys.urls')),

    # Documents URLs
    path("api/documents/", include('documents.urls')),

    # API Documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
]

# Serve uploaded files during development
if settings.DEBUG:
    # Get MEDIA_PREFIX from settings (e.g., '/files/')
    media_prefix = getattr(settings, 'MEDIA_PREFIX', '/media/').strip('/')

    urlpatterns += static(
        f'/{media_prefix}/',
        document_root=settings.MEDIA_ROOT
    )
